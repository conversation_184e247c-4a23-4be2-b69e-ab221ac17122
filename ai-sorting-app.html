<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分拣系统 - 移动端原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            border: 8px solid #1f2937;
            border-radius: 40px;
            overflow: hidden;
            background: #000;
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #f8fafc;
            position: relative;
            overflow: hidden;
        }
        .status-bar {
            height: 44px;
            background: #1f2937;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .page {
            display: none;
            height: calc(100% - 44px - 80px);
            overflow-y: auto;
        }
        .page.active {
            display: block;
        }
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 10px 0;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .nav-item.active {
            color: #3b82f6;
        }
        .ai-button {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .header {
            background: white;
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin: 12px 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }
        .status-processing {
            background: #dbeafe;
            color: #1e40af;
        }
        .landscape-page {
            transform: rotate(90deg);
            transform-origin: center;
            width: 812px;
            height: 375px;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-left: -406px;
            margin-top: -187.5px;
        }
        .landscape-container {
            width: 100%;
            height: 100%;
            display: flex;
            background: #f8fafc;
        }
        .sorting-sidebar {
            width: 200px;
            background: white;
            border-right: 1px solid #e5e7eb;
            overflow-y: auto;
        }
        .sorting-main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .sorting-products {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }
        .sorting-cart {
            width: 280px;
            background: white;
            border-left: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }
        .product-item {
            aspect-ratio: 1;
            background: white;
            border-radius: 8px;
            padding: 8px;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .product-item:hover {
            border-color: #3b82f6;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>AI分拣系统</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>

            <!-- 主页面 -->
            <div id="home" class="page active">
                <div class="header">
                    <h1 class="text-xl font-bold text-gray-800">工作台</h1>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-bell text-gray-600"></i>
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                    </div>
                </div>

                <!-- 快捷功能卡片 -->
                <div class="p-4">
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="card cursor-pointer hover:shadow-lg transition-shadow" onclick="showPage('warehouse')">
                            <div class="flex items-center justify-between mb-3">
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-warehouse text-blue-600 text-xl"></i>
                                </div>
                                <span class="status-badge status-pending">3待入库</span>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-1">入库管理</h3>
                            <p class="text-sm text-gray-600">进货单管理</p>
                        </div>

                        <div class="card cursor-pointer hover:shadow-lg transition-shadow" onclick="showPage('order')">
                            <div class="flex items-center justify-between mb-3">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-clipboard-list text-green-600 text-xl"></i>
                                </div>
                                <span class="status-badge status-processing">录单中</span>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-1">录单功能</h3>
                            <p class="text-sm text-gray-600">AI辅助录单</p>
                        </div>

                        <div class="card cursor-pointer hover:shadow-lg transition-shadow" onclick="showSortingPage()">
                            <div class="flex items-center justify-between mb-3">
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-sort-amount-up text-purple-600 text-xl"></i>
                                </div>
                                <span class="status-badge status-completed">12已完成</span>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-1">分拣系统</h3>
                            <p class="text-sm text-gray-600">商品分拣作业</p>
                        </div>

                        <div class="card cursor-pointer hover:shadow-lg transition-shadow" onclick="showPage('finance')">
                            <div class="flex items-center justify-between mb-3">
                                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-chart-line text-yellow-600 text-xl"></i>
                                </div>
                                <span class="status-badge status-completed">今日盈利</span>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-1">财务管理</h3>
                            <p class="text-sm text-gray-600">收益统计分析</p>
                        </div>

                        <div class="card cursor-pointer hover:shadow-lg transition-shadow" onclick="showPage('sales')">
                            <div class="flex items-center justify-between mb-3">
                                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-cash-register text-red-600 text-xl"></i>
                                </div>
                                <span class="status-badge status-processing">营业中</span>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-1">快速售卖</h3>
                            <p class="text-sm text-gray-600">现货销售</p>
                        </div>

                        <div class="card cursor-pointer hover:shadow-lg transition-shadow" onclick="showPage('dashboard')">
                            <div class="flex items-center justify-between mb-3">
                                <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-tachometer-alt text-indigo-600 text-xl"></i>
                                </div>
                                <span class="status-badge status-processing">实时监控</span>
                            </div>
                            <h3 class="font-semibold text-gray-800 mb-1">中控台</h3>
                            <p class="text-sm text-gray-600">数据监控</p>
                        </div>
                    </div>

                    <!-- 今日概览 -->
                    <div class="card">
                        <h3 class="font-semibold text-gray-800 mb-4">今日概览</h3>
                        <div class="grid grid-cols-3 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600">156</div>
                                <div class="text-sm text-gray-600">订单数</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600">89%</div>
                                <div class="text-sm text-gray-600">完成率</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-600">¥12.8k</div>
                                <div class="text-sm text-gray-600">营业额</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 入库管理页面 -->
            <div id="warehouse" class="page">
                <div class="header">
                    <div class="flex items-center">
                        <i class="fas fa-arrow-left text-gray-600 mr-3 cursor-pointer" onclick="showPage('home')"></i>
                        <h1 class="text-xl font-bold text-gray-800">入库管理</h1>
                    </div>
                    <i class="fas fa-plus text-blue-600 cursor-pointer"></i>
                </div>

                <!-- 筛选标签 -->
                <div class="px-4 py-3 bg-white border-b border-gray-200">
                    <div class="flex space-x-3">
                        <button class="px-4 py-2 bg-blue-500 text-white rounded-full text-sm font-medium">全部</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium">待入库</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium">已入库</button>
                    </div>
                </div>

                <div class="p-4 space-y-4">
                    <!-- 入库订单卡片 -->
                    <div class="card cursor-pointer" onclick="showPage('warehouse-detail')">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-box text-orange-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800">进货单 #PO2024001</h3>
                                    <p class="text-sm text-gray-600">供应商：绿源蔬菜批发</p>
                                </div>
                            </div>
                            <span class="status-badge status-pending">待入库</span>
                        </div>
                        <div class="flex justify-between items-center text-sm text-gray-600">
                            <span>商品种类：8种</span>
                            <span>总金额：¥2,580</span>
                            <span>2024-01-15 09:30</span>
                        </div>
                    </div>

                    <div class="card cursor-pointer" onclick="showPage('warehouse-detail')">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-check text-green-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800">进货单 #PO2024002</h3>
                                    <p class="text-sm text-gray-600">供应商：新鲜果蔬园</p>
                                </div>
                            </div>
                            <span class="status-badge status-completed">已入库</span>
                        </div>
                        <div class="flex justify-between items-center text-sm text-gray-600">
                            <span>商品种类：12种</span>
                            <span>总金额：¥3,240</span>
                            <span>2024-01-15 08:15</span>
                        </div>
                    </div>

                    <div class="card cursor-pointer" onclick="showPage('warehouse-detail')">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-box text-orange-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800">进货单 #PO2024003</h3>
                                    <p class="text-sm text-gray-600">供应商：山东寿光蔬菜</p>
                                </div>
                            </div>
                            <span class="status-badge status-pending">待入库</span>
                        </div>
                        <div class="flex justify-between items-center text-sm text-gray-600">
                            <span>商品种类：6种</span>
                            <span>总金额：¥1,890</span>
                            <span>2024-01-15 07:45</span>
                        </div>
                    </div>

                    <div class="card cursor-pointer" onclick="showPage('warehouse-detail')">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-clock text-blue-600"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-800">进货单 #PO2024004</h3>
                                    <p class="text-sm text-gray-600">供应商：有机农场直供</p>
                                </div>
                            </div>
                            <span class="status-badge status-processing">入库中</span>
                        </div>
                        <div class="flex justify-between items-center text-sm text-gray-600">
                            <span>商品种类：15种</span>
                            <span>总金额：¥4,560</span>
                            <span>2024-01-15 06:30</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 入库详情页面 -->
            <div id="warehouse-detail" class="page">
                <div class="header">
                    <div class="flex items-center">
                        <i class="fas fa-arrow-left text-gray-600 mr-3 cursor-pointer" onclick="showPage('warehouse')"></i>
                        <h1 class="text-xl font-bold text-gray-800">入库详情</h1>
                    </div>
                    <i class="fas fa-edit text-blue-600 cursor-pointer"></i>
                </div>

                <div class="p-4">
                    <!-- 订单信息 -->
                    <div class="card mb-4">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-semibold text-gray-800">进货单 #PO2024001</h3>
                            <span class="status-badge status-pending">待入库</span>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">供应商：</span>
                                <span class="font-medium">绿源蔬菜批发</span>
                            </div>
                            <div>
                                <span class="text-gray-600">订单时间：</span>
                                <span class="font-medium">2024-01-15 09:30</span>
                            </div>
                            <div>
                                <span class="text-gray-600">商品种类：</span>
                                <span class="font-medium">8种</span>
                            </div>
                            <div>
                                <span class="text-gray-600">总金额：</span>
                                <span class="font-medium text-red-600">¥2,580</span>
                            </div>
                        </div>
                    </div>

                    <!-- 商品清单 -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-semibold text-gray-800">商品清单</h3>
                            <button class="px-4 py-2 bg-blue-500 text-white rounded-lg text-sm font-medium">
                                <i class="fas fa-check mr-2"></i>确认入库
                            </button>
                        </div>

                        <div class="space-y-3">
                            <!-- 已入库商品 -->
                            <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-gray-800">小白菜</h4>
                                        <p class="text-sm text-gray-600">规格：5斤/袋</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-medium text-gray-800">5斤</div>
                                    <div class="text-sm text-green-600">已入库</div>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-check-circle text-green-600 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-gray-800">大白菜</h4>
                                        <p class="text-sm text-gray-600">规格：6斤/袋</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-medium text-gray-800">6斤</div>
                                    <div class="text-sm text-green-600">已入库</div>
                                </div>
                            </div>

                            <!-- 待入库商品 -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-circle text-gray-400 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-gray-800">蘑菇</h4>
                                        <p class="text-sm text-gray-600">规格：1箱/20盒</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-medium text-gray-800">1箱</div>
                                    <div class="text-sm text-orange-600">待入库</div>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-circle text-gray-400 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-gray-800">胡萝卜</h4>
                                        <p class="text-sm text-gray-600">规格：3斤/袋</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-medium text-gray-800">8袋</div>
                                    <div class="text-sm text-orange-600">待入库</div>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-circle text-gray-400 mr-3"></i>
                                    <div>
                                        <h4 class="font-medium text-gray-800">土豆</h4>
                                        <p class="text-sm text-gray-600">规格：10斤/袋</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-medium text-gray-800">3袋</div>
                                    <div class="text-sm text-orange-600">待入库</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 录单功能页面 -->
            <div id="order" class="page">
                <div class="header">
                    <div class="flex items-center">
                        <i class="fas fa-arrow-left text-gray-600 mr-3 cursor-pointer" onclick="showPage('home')"></i>
                        <h1 class="text-xl font-bold text-gray-800">录单功能</h1>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-microphone text-blue-600 cursor-pointer"></i>
                        <i class="fas fa-save text-gray-600 cursor-pointer"></i>
                    </div>
                </div>

                <!-- AI助手提示 -->
                <div class="px-4 py-3 bg-blue-50 border-b border-blue-100">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-robot text-white text-sm"></i>
                        </div>
                        <div>
                            <p class="text-sm text-blue-800 font-medium">AI助手已就绪</p>
                            <p class="text-xs text-blue-600">点击语音按钮开始录单，或手动添加商品</p>
                        </div>
                    </div>
                </div>

                <div class="p-4">
                    <!-- 客户信息 -->
                    <div class="card mb-4">
                        <h3 class="font-semibold text-gray-800 mb-3">客户信息</h3>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">客户姓名</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="请输入客户姓名" value="张三">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">联系电话</label>
                                <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="请输入联系电话" value="138****8888">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">配送地址</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="请输入配送地址" value="北京市朝阳区xxx街道">
                            </div>
                        </div>
                    </div>

                    <!-- 商品列表 -->
                    <div class="card mb-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="font-semibold text-gray-800">商品列表</h3>
                            <button class="px-3 py-1 bg-blue-500 text-white rounded-lg text-sm" onclick="showPage('add-product')">
                                <i class="fas fa-plus mr-1"></i>添加商品
                            </button>
                        </div>

                        <div class="space-y-3">
                            <!-- 商品项 -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <img src="https://images.unsplash.com/photo-1540420773420-3366772f4999?w=50&h=50&fit=crop&crop=center"
                                         class="w-12 h-12 rounded-lg object-cover mr-3" alt="小白菜">
                                    <div>
                                        <h4 class="font-medium text-gray-800">小白菜</h4>
                                        <p class="text-sm text-gray-600">¥3.5/斤</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="flex items-center space-x-2">
                                        <button class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                            <i class="fas fa-minus text-gray-600 text-sm"></i>
                                        </button>
                                        <span class="w-12 text-center font-medium">5斤</span>
                                        <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                            <i class="fas fa-plus text-white text-sm"></i>
                                        </button>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-medium text-gray-800">¥17.5</div>
                                    </div>
                                    <button class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-trash text-red-600 text-sm"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <img src="https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=50&h=50&fit=crop&crop=center"
                                         class="w-12 h-12 rounded-lg object-cover mr-3" alt="大白菜">
                                    <div>
                                        <h4 class="font-medium text-gray-800">大白菜</h4>
                                        <p class="text-sm text-gray-600">¥2.8/斤</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="flex items-center space-x-2">
                                        <button class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                            <i class="fas fa-minus text-gray-600 text-sm"></i>
                                        </button>
                                        <span class="w-12 text-center font-medium">6斤</span>
                                        <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                            <i class="fas fa-plus text-white text-sm"></i>
                                        </button>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-medium text-gray-800">¥16.8</div>
                                    </div>
                                    <button class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-trash text-red-600 text-sm"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center">
                                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=50&h=50&fit=crop&crop=center"
                                         class="w-12 h-12 rounded-lg object-cover mr-3" alt="蘑菇">
                                    <div>
                                        <h4 class="font-medium text-gray-800">蘑菇</h4>
                                        <p class="text-sm text-gray-600">¥12.0/盒</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="flex items-center space-x-2">
                                        <button class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                            <i class="fas fa-minus text-gray-600 text-sm"></i>
                                        </button>
                                        <span class="w-12 text-center font-medium">2盒</span>
                                        <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                            <i class="fas fa-plus text-white text-sm"></i>
                                        </button>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-medium text-gray-800">¥24.0</div>
                                    </div>
                                    <button class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-trash text-red-600 text-sm"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 订单汇总 -->
                    <div class="card mb-4">
                        <h3 class="font-semibold text-gray-800 mb-3">订单汇总</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">商品总额</span>
                                <span class="font-medium">¥58.3</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">配送费</span>
                                <span class="font-medium">¥5.0</span>
                            </div>
                            <div class="border-t border-gray-200 pt-2 flex justify-between">
                                <span class="font-semibold text-gray-800">合计金额</span>
                                <span class="font-bold text-red-600 text-lg">¥63.3</span>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="flex space-x-3">
                        <button class="flex-1 py-3 bg-gray-200 text-gray-700 rounded-lg font-medium">
                            保存草稿
                        </button>
                        <button class="flex-1 py-3 bg-blue-500 text-white rounded-lg font-medium">
                            <i class="fas fa-check mr-2"></i>确认下单
                        </button>
                    </div>
                </div>
            </div>

            <!-- 添加商品页面 -->
            <div id="add-product" class="page">
                <div class="header">
                    <div class="flex items-center">
                        <i class="fas fa-arrow-left text-gray-600 mr-3 cursor-pointer" onclick="showPage('order')"></i>
                        <h1 class="text-xl font-bold text-gray-800">添加商品</h1>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-search text-gray-600 cursor-pointer"></i>
                    </div>
                </div>

                <!-- 搜索栏 -->
                <div class="px-4 py-3 bg-white border-b border-gray-200">
                    <div class="relative">
                        <input type="text" class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="搜索商品名称">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <!-- 商品分类 -->
                <div class="px-4 py-3 bg-white border-b border-gray-200">
                    <div class="flex space-x-3 overflow-x-auto">
                        <button class="px-4 py-2 bg-blue-500 text-white rounded-full text-sm font-medium whitespace-nowrap">全部</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium whitespace-nowrap">叶菜类</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium whitespace-nowrap">根茎类</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium whitespace-nowrap">菌菇类</button>
                        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium whitespace-nowrap">水果类</button>
                    </div>
                </div>

                <div class="p-4">
                    <div class="grid grid-cols-2 gap-4">
                        <!-- 商品卡片 -->
                        <div class="card cursor-pointer hover:shadow-lg transition-shadow">
                            <img src="https://images.unsplash.com/photo-1540420773420-3366772f4999?w=150&h=100&fit=crop&crop=center"
                                 class="w-full h-24 rounded-lg object-cover mb-3" alt="小白菜">
                            <h4 class="font-medium text-gray-800 mb-1">小白菜</h4>
                            <p class="text-sm text-gray-600 mb-2">新鲜叶菜</p>
                            <div class="flex items-center justify-between">
                                <span class="font-bold text-red-600">¥3.5/斤</span>
                                <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-plus text-white text-sm"></i>
                                </button>
                            </div>
                        </div>

                        <div class="card cursor-pointer hover:shadow-lg transition-shadow">
                            <img src="https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=150&h=100&fit=crop&crop=center"
                                 class="w-full h-24 rounded-lg object-cover mb-3" alt="大白菜">
                            <h4 class="font-medium text-gray-800 mb-1">大白菜</h4>
                            <p class="text-sm text-gray-600 mb-2">优质叶菜</p>
                            <div class="flex items-center justify-between">
                                <span class="font-bold text-red-600">¥2.8/斤</span>
                                <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-plus text-white text-sm"></i>
                                </button>
                            </div>
                        </div>

                        <div class="card cursor-pointer hover:shadow-lg transition-shadow">
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=150&h=100&fit=crop&crop=center"
                                 class="w-full h-24 rounded-lg object-cover mb-3" alt="蘑菇">
                            <h4 class="font-medium text-gray-800 mb-1">蘑菇</h4>
                            <p class="text-sm text-gray-600 mb-2">新鲜菌菇</p>
                            <div class="flex items-center justify-between">
                                <span class="font-bold text-red-600">¥12.0/盒</span>
                                <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-plus text-white text-sm"></i>
                                </button>
                            </div>
                        </div>

                        <div class="card cursor-pointer hover:shadow-lg transition-shadow">
                            <img src="https://images.unsplash.com/photo-1598170845058-32b9d6a5da37?w=150&h=100&fit=crop&crop=center"
                                 class="w-full h-24 rounded-lg object-cover mb-3" alt="胡萝卜">
                            <h4 class="font-medium text-gray-800 mb-1">胡萝卜</h4>
                            <p class="text-sm text-gray-600 mb-2">有机根茎</p>
                            <div class="flex items-center justify-between">
                                <span class="font-bold text-red-600">¥4.2/斤</span>
                                <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-plus text-white text-sm"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的页面 -->
            <div id="profile" class="page">
                <div class="header">
                    <h1 class="text-xl font-bold text-gray-800">我的</h1>
                    <i class="fas fa-cog text-gray-600"></i>
                </div>
                <div class="p-4">
                    <div class="card">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user text-white text-2xl"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">管理员</h3>
                                <p class="text-sm text-gray-600"><EMAIL></p>
                            </div>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="flex items-center justify-between py-3 border-b border-gray-100 cursor-pointer" onclick="showPage('members')">
                                <div class="flex items-center">
                                    <i class="fas fa-users text-gray-600 w-6"></i>
                                    <span class="ml-3">会员中心</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            
                            <div class="flex items-center justify-between py-3 border-b border-gray-100">
                                <div class="flex items-center">
                                    <i class="fas fa-shield-alt text-gray-600 w-6"></i>
                                    <span class="ml-3">权限管理</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            
                            <div class="flex items-center justify-between py-3 border-b border-gray-100">
                                <div class="flex items-center">
                                    <i class="fas fa-bell text-gray-600 w-6"></i>
                                    <span class="ml-3">消息通知</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                            
                            <div class="flex items-center justify-between py-3">
                                <div class="flex items-center">
                                    <i class="fas fa-sign-out-alt text-gray-600 w-6"></i>
                                    <span class="ml-3">退出登录</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分拣系统页面（横屏） -->
            <div id="sorting" class="page">
                <div class="landscape-page">
                    <div class="landscape-container">
                        <!-- 左侧分类栏 -->
                        <div class="sorting-sidebar">
                            <div class="p-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <h2 class="font-bold text-gray-800">分拣系统</h2>
                                    <button onclick="showPage('home')" class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-times text-gray-600"></i>
                                    </button>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">订单 #SO2024001</p>
                            </div>

                            <div class="p-3">
                                <h3 class="font-semibold text-gray-800 mb-3">商品分类</h3>
                                <div class="space-y-2">
                                    <button class="w-full text-left px-3 py-2 bg-blue-500 text-white rounded-lg text-sm font-medium">
                                        <i class="fas fa-leaf mr-2"></i>叶菜类
                                    </button>
                                    <button class="w-full text-left px-3 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200">
                                        <i class="fas fa-carrot mr-2"></i>根茎类
                                    </button>
                                    <button class="w-full text-left px-3 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200">
                                        <i class="fas fa-seedling mr-2"></i>菌菇类
                                    </button>
                                    <button class="w-full text-left px-3 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200">
                                        <i class="fas fa-apple-alt mr-2"></i>水果类
                                    </button>
                                </div>
                            </div>

                            <div class="p-3 border-t border-gray-200">
                                <h3 class="font-semibold text-gray-800 mb-3">分拣进度</h3>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">已完成</span>
                                        <span class="font-medium text-green-600">8/12</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-green-500 h-2 rounded-full" style="width: 67%"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 中间商品区域 -->
                        <div class="sorting-main">
                            <div class="p-4 border-b border-gray-200 bg-white">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-gray-800">叶菜类商品</h3>
                                    <div class="flex items-center space-x-4">
                                        <span class="text-sm text-gray-600">当前分类：4种商品</span>
                                        <button class="px-3 py-1 bg-green-500 text-white rounded-lg text-sm">
                                            <i class="fas fa-check mr-1"></i>完成分类
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="sorting-products">
                                <div class="product-grid">
                                    <!-- 商品项 -->
                                    <div class="product-item" onclick="addToSortingCart('小白菜', '5斤')">
                                        <img src="https://images.unsplash.com/photo-1540420773420-3366772f4999?w=80&h=60&fit=crop&crop=center"
                                             class="w-16 h-12 rounded object-cover mb-2" alt="小白菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">小白菜</h4>
                                        <p class="text-xs text-gray-600">需要: 5斤</p>
                                        <div class="mt-1 px-2 py-1 bg-orange-100 text-orange-600 rounded text-xs">待分拣</div>
                                    </div>

                                    <div class="product-item" onclick="addToSortingCart('大白菜', '6斤')">
                                        <img src="https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=80&h=60&fit=crop&crop=center"
                                             class="w-16 h-12 rounded object-cover mb-2" alt="大白菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">大白菜</h4>
                                        <p class="text-xs text-gray-600">需要: 6斤</p>
                                        <div class="mt-1 px-2 py-1 bg-green-100 text-green-600 rounded text-xs">已完成</div>
                                    </div>

                                    <div class="product-item" onclick="addToSortingCart('菠菜', '3斤')">
                                        <img src="https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=80&h=60&fit=crop&crop=center"
                                             class="w-16 h-12 rounded object-cover mb-2" alt="菠菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">菠菜</h4>
                                        <p class="text-xs text-gray-600">需要: 3斤</p>
                                        <div class="mt-1 px-2 py-1 bg-orange-100 text-orange-600 rounded text-xs">待分拣</div>
                                    </div>

                                    <div class="product-item" onclick="addToSortingCart('生菜', '4斤')">
                                        <img src="https://images.unsplash.com/photo-1622206151226-18ca2c9ab4a1?w=80&h=60&fit=crop&crop=center"
                                             class="w-16 h-12 rounded object-cover mb-2" alt="生菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">生菜</h4>
                                        <p class="text-xs text-gray-600">需要: 4斤</p>
                                        <div class="mt-1 px-2 py-1 bg-blue-100 text-blue-600 rounded text-xs">分拣中</div>
                                    </div>

                                    <div class="product-item" onclick="addToSortingCart('韭菜', '2斤')">
                                        <img src="https://images.unsplash.com/photo-1590779033100-9f60a05a013d?w=80&h=60&fit=crop&crop=center"
                                             class="w-16 h-12 rounded object-cover mb-2" alt="韭菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">韭菜</h4>
                                        <p class="text-xs text-gray-600">需要: 2斤</p>
                                        <div class="mt-1 px-2 py-1 bg-orange-100 text-orange-600 rounded text-xs">待分拣</div>
                                    </div>

                                    <div class="product-item" onclick="addToSortingCart('芹菜', '3斤')">
                                        <img src="https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=80&h=60&fit=crop&crop=center"
                                             class="w-16 h-12 rounded object-cover mb-2" alt="芹菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">芹菜</h4>
                                        <p class="text-xs text-gray-600">需要: 3斤</p>
                                        <div class="mt-1 px-2 py-1 bg-green-100 text-green-600 rounded text-xs">已完成</div>
                                    </div>

                                    <div class="product-item" onclick="addToSortingCart('油菜', '4斤')">
                                        <img src="https://images.unsplash.com/photo-1590779033100-9f60a05a013d?w=80&h=60&fit=crop&crop=center"
                                             class="w-16 h-12 rounded object-cover mb-2" alt="油菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">油菜</h4>
                                        <p class="text-xs text-gray-600">需要: 4斤</p>
                                        <div class="mt-1 px-2 py-1 bg-orange-100 text-orange-600 rounded text-xs">待分拣</div>
                                    </div>

                                    <div class="product-item" onclick="addToSortingCart('茼蒿', '2斤')">
                                        <img src="https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=80&h=60&fit=crop&crop=center"
                                             class="w-16 h-12 rounded object-cover mb-2" alt="茼蒿">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">茼蒿</h4>
                                        <p class="text-xs text-gray-600">需要: 2斤</p>
                                        <div class="mt-1 px-2 py-1 bg-orange-100 text-orange-600 rounded text-xs">待分拣</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧已分拣列表 -->
                        <div class="sorting-cart">
                            <div class="p-4 border-b border-gray-200">
                                <h3 class="font-semibold text-gray-800">已分拣商品</h3>
                                <p class="text-sm text-gray-600">订单 #SO2024001</p>
                            </div>

                            <div class="flex-1 overflow-y-auto p-4">
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                            <div>
                                                <h4 class="font-medium text-gray-800 text-sm">大白菜</h4>
                                                <p class="text-xs text-gray-600">已分拣: 6斤</p>
                                            </div>
                                        </div>
                                        <button class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-times text-red-600 text-xs"></i>
                                        </button>
                                    </div>

                                    <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-check-circle text-green-600 mr-2"></i>
                                            <div>
                                                <h4 class="font-medium text-gray-800 text-sm">芹菜</h4>
                                                <p class="text-xs text-gray-600">已分拣: 3斤</p>
                                            </div>
                                        </div>
                                        <button class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-times text-red-600 text-xs"></i>
                                        </button>
                                    </div>

                                    <div class="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                                        <div class="flex items-center">
                                            <i class="fas fa-clock text-blue-600 mr-2"></i>
                                            <div>
                                                <h4 class="font-medium text-gray-800 text-sm">生菜</h4>
                                                <p class="text-xs text-gray-600">分拣中: 2/4斤</p>
                                            </div>
                                        </div>
                                        <button class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                                            <i class="fas fa-times text-red-600 text-xs"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="p-4 border-t border-gray-200">
                                <div class="space-y-3">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">已完成商品</span>
                                        <span class="font-medium">8/12</span>
                                    </div>
                                    <button class="w-full py-2 bg-green-500 text-white rounded-lg font-medium text-sm">
                                        <i class="fas fa-check mr-2"></i>完成分拣
                                    </button>
                                    <button class="w-full py-2 bg-gray-200 text-gray-700 rounded-lg font-medium text-sm">
                                        暂停分拣
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 财务管理页面 -->
            <div id="finance" class="page">
                <div class="header">
                    <div class="flex items-center">
                        <i class="fas fa-arrow-left text-gray-600 mr-3 cursor-pointer" onclick="showPage('home')"></i>
                        <h1 class="text-xl font-bold text-gray-800">财务管理</h1>
                    </div>
                    <i class="fas fa-calendar-alt text-gray-600 cursor-pointer"></i>
                </div>

                <div class="p-4">
                    <!-- 财务概览 -->
                    <div class="card mb-4">
                        <h3 class="font-semibold text-gray-800 mb-4">今日财务概览</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">¥12,580</div>
                                <div class="text-sm text-gray-600 mt-1">今日收入</div>
                                <div class="text-xs text-green-600 mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>+15.2%
                                </div>
                            </div>
                            <div class="text-center p-4 bg-red-50 rounded-lg">
                                <div class="text-2xl font-bold text-red-600">¥8,240</div>
                                <div class="text-sm text-gray-600 mt-1">今日支出</div>
                                <div class="text-xs text-red-600 mt-1">
                                    <i class="fas fa-arrow-down mr-1"></i>-8.5%
                                </div>
                            </div>
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600">¥4,340</div>
                                <div class="text-sm text-gray-600 mt-1">净利润</div>
                                <div class="text-xs text-blue-600 mt-1">
                                    <i class="fas fa-arrow-up mr-1"></i>+22.8%
                                </div>
                            </div>
                            <div class="text-center p-4 bg-orange-50 rounded-lg">
                                <div class="text-2xl font-bold text-orange-600">¥15,680</div>
                                <div class="text-sm text-gray-600 mt-1">待收款</div>
                                <div class="text-xs text-orange-600 mt-1">
                                    <i class="fas fa-clock mr-1"></i>12笔
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 收益趋势图 -->
                    <div class="card mb-4">
                        <h3 class="font-semibold text-gray-800 mb-4">7天收益趋势</h3>
                        <div class="h-32 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg flex items-end justify-around p-4">
                            <div class="flex flex-col items-center">
                                <div class="w-6 bg-blue-500 rounded-t" style="height: 60px;"></div>
                                <span class="text-xs text-gray-600 mt-2">周一</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-6 bg-blue-500 rounded-t" style="height: 80px;"></div>
                                <span class="text-xs text-gray-600 mt-2">周二</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-6 bg-blue-500 rounded-t" style="height: 45px;"></div>
                                <span class="text-xs text-gray-600 mt-2">周三</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-6 bg-blue-500 rounded-t" style="height: 90px;"></div>
                                <span class="text-xs text-gray-600 mt-2">周四</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-6 bg-blue-500 rounded-t" style="height: 70px;"></div>
                                <span class="text-xs text-gray-600 mt-2">周五</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-6 bg-green-500 rounded-t" style="height: 100px;"></div>
                                <span class="text-xs text-gray-600 mt-2">周六</span>
                            </div>
                            <div class="flex flex-col items-center">
                                <div class="w-6 bg-green-500 rounded-t" style="height: 85px;"></div>
                                <span class="text-xs text-gray-600 mt-2">周日</span>
                            </div>
                        </div>
                    </div>

                    <!-- 快捷操作 -->
                    <div class="grid grid-cols-3 gap-3 mb-4">
                        <button class="card text-center py-4 hover:shadow-lg transition-shadow" onclick="showPage('debt-management')">
                            <i class="fas fa-file-invoice-dollar text-orange-600 text-2xl mb-2"></i>
                            <div class="font-medium text-gray-800">欠款管理</div>
                            <div class="text-sm text-gray-600">12笔待收</div>
                        </button>

                        <button class="card text-center py-4 hover:shadow-lg transition-shadow" onclick="showPage('expense-record')">
                            <i class="fas fa-receipt text-red-600 text-2xl mb-2"></i>
                            <div class="font-medium text-gray-800">支出记录</div>
                            <div class="text-sm text-gray-600">今日8笔</div>
                        </button>

                        <button class="card text-center py-4 hover:shadow-lg transition-shadow" onclick="showPage('income-record')">
                            <i class="fas fa-coins text-green-600 text-2xl mb-2"></i>
                            <div class="font-medium text-gray-800">收入记录</div>
                            <div class="text-sm text-gray-600">今日25笔</div>
                        </button>
                    </div>

                    <!-- 最近交易 -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="font-semibold text-gray-800">最近交易</h3>
                            <button class="text-blue-600 text-sm">查看全部</button>
                        </div>

                        <div class="space-y-3">
                            <div class="flex items-center justify-between py-2 border-b border-gray-100">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-plus text-green-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800 text-sm">订单收款</h4>
                                        <p class="text-xs text-gray-600">张三 - 订单#SO2024001</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-medium text-green-600">+¥158.5</div>
                                    <div class="text-xs text-gray-600">10:30</div>
                                </div>
                            </div>

                            <div class="flex items-center justify-between py-2 border-b border-gray-100">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-minus text-red-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800 text-sm">采购支出</h4>
                                        <p class="text-xs text-gray-600">绿源蔬菜批发</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-medium text-red-600">-¥2,580</div>
                                    <div class="text-xs text-gray-600">09:15</div>
                                </div>
                            </div>

                            <div class="flex items-center justify-between py-2">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-plus text-green-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800 text-sm">订单收款</h4>
                                        <p class="text-xs text-gray-600">李四 - 订单#SO2024002</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-medium text-green-600">+¥234.8</div>
                                    <div class="text-xs text-gray-600">08:45</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 欠款管理页面 -->
            <div id="debt-management" class="page">
                <div class="header">
                    <div class="flex items-center">
                        <i class="fas fa-arrow-left text-gray-600 mr-3 cursor-pointer" onclick="showPage('finance')"></i>
                        <h1 class="text-xl font-bold text-gray-800">欠款管理</h1>
                    </div>
                    <i class="fas fa-plus text-blue-600 cursor-pointer"></i>
                </div>

                <div class="p-4">
                    <!-- 欠款统计 -->
                    <div class="card mb-4">
                        <h3 class="font-semibold text-gray-800 mb-3">欠款统计</h3>
                        <div class="grid grid-cols-3 gap-4">
                            <div class="text-center">
                                <div class="text-xl font-bold text-orange-600">¥15,680</div>
                                <div class="text-sm text-gray-600">总欠款</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-red-600">12笔</div>
                                <div class="text-sm text-gray-600">欠款订单</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-blue-600">5天</div>
                                <div class="text-sm text-gray-600">平均账期</div>
                            </div>
                        </div>
                    </div>

                    <!-- 欠款列表 -->
                    <div class="space-y-3">
                        <div class="card">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-orange-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800">王五</h4>
                                        <p class="text-sm text-gray-600">订单 #SO2024003</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-bold text-red-600">¥3,240</div>
                                    <div class="text-sm text-gray-600">逾期2天</div>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <button class="flex-1 py-2 bg-blue-500 text-white rounded-lg text-sm">催收</button>
                                <button class="flex-1 py-2 bg-green-500 text-white rounded-lg text-sm">标记已收</button>
                            </div>
                        </div>

                        <div class="card">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-orange-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800">赵六</h4>
                                        <p class="text-sm text-gray-600">订单 #SO2024004</p>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="font-bold text-orange-600">¥1,890</div>
                                    <div class="text-sm text-gray-600">还有1天</div>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <button class="flex-1 py-2 bg-blue-500 text-white rounded-lg text-sm">催收</button>
                                <button class="flex-1 py-2 bg-green-500 text-white rounded-lg text-sm">标记已收</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中控台页面 -->
            <div id="dashboard" class="page">
                <div class="header">
                    <div class="flex items-center">
                        <i class="fas fa-arrow-left text-gray-600 mr-3 cursor-pointer" onclick="showPage('home')"></i>
                        <h1 class="text-xl font-bold text-gray-800">中控台</h1>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-sync-alt text-gray-600 cursor-pointer"></i>
                        <i class="fas fa-expand text-gray-600 cursor-pointer"></i>
                    </div>
                </div>

                <div class="p-4">
                    <!-- 实时状态卡片 -->
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="card bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold">156</div>
                                    <div class="text-sm opacity-90">今日订单</div>
                                </div>
                                <i class="fas fa-clipboard-list text-3xl opacity-75"></i>
                            </div>
                            <div class="mt-2 text-xs opacity-75">
                                <i class="fas fa-arrow-up mr-1"></i>+12% 较昨日
                            </div>
                        </div>

                        <div class="card bg-gradient-to-r from-green-500 to-green-600 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold">89%</div>
                                    <div class="text-sm opacity-90">分拣完成率</div>
                                </div>
                                <i class="fas fa-check-circle text-3xl opacity-75"></i>
                            </div>
                            <div class="mt-2 text-xs opacity-75">
                                139/156 已完成
                            </div>
                        </div>

                        <div class="card bg-gradient-to-r from-purple-500 to-purple-600 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold">17</div>
                                    <div class="text-sm opacity-90">分拣中订单</div>
                                </div>
                                <i class="fas fa-clock text-3xl opacity-75"></i>
                            </div>
                            <div class="mt-2 text-xs opacity-75">
                                预计30分钟完成
                            </div>
                        </div>

                        <div class="card bg-gradient-to-r from-orange-500 to-orange-600 text-white">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-2xl font-bold">8</div>
                                    <div class="text-sm opacity-90">在线人数</div>
                                </div>
                                <i class="fas fa-users text-3xl opacity-75"></i>
                            </div>
                            <div class="mt-2 text-xs opacity-75">
                                5分拣员 + 3录单员
                            </div>
                        </div>
                    </div>

                    <!-- 实时监控 -->
                    <div class="card mb-4">
                        <h3 class="font-semibold text-gray-800 mb-4">实时监控</h3>
                        <div class="space-y-4">
                            <!-- 分拣进度条 -->
                            <div>
                                <div class="flex justify-between text-sm mb-2">
                                    <span class="text-gray-600">整体分拣进度</span>
                                    <span class="font-medium">89% (139/156)</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-3">
                                    <div class="bg-green-500 h-3 rounded-full transition-all duration-500" style="width: 89%"></div>
                                </div>
                            </div>

                            <!-- 各分拣员状态 -->
                            <div>
                                <h4 class="font-medium text-gray-800 mb-3">分拣员状态</h4>
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between p-2 bg-green-50 rounded-lg">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                            <span class="text-sm font-medium">张三</span>
                                        </div>
                                        <div class="text-sm text-gray-600">正在分拣 #SO2024001</div>
                                        <div class="text-sm font-medium text-green-600">85%</div>
                                    </div>

                                    <div class="flex items-center justify-between p-2 bg-blue-50 rounded-lg">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                                            <span class="text-sm font-medium">李四</span>
                                        </div>
                                        <div class="text-sm text-gray-600">正在分拣 #SO2024002</div>
                                        <div class="text-sm font-medium text-blue-600">92%</div>
                                    </div>

                                    <div class="flex items-center justify-between p-2 bg-orange-50 rounded-lg">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
                                            <span class="text-sm font-medium">王五</span>
                                        </div>
                                        <div class="text-sm text-gray-600">休息中</div>
                                        <div class="text-sm font-medium text-orange-600">--</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 今日数据统计 -->
                    <div class="card mb-4">
                        <h3 class="font-semibold text-gray-800 mb-4">今日数据统计</h3>
                        <div class="grid grid-cols-3 gap-4">
                            <div class="text-center">
                                <div class="text-xl font-bold text-blue-600">¥12.8k</div>
                                <div class="text-sm text-gray-600">营业额</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-green-600">¥4.2k</div>
                                <div class="text-sm text-gray-600">净利润</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-purple-600">2.8h</div>
                                <div class="text-sm text-gray-600">平均分拣时间</div>
                            </div>
                        </div>
                    </div>

                    <!-- 异常警报 -->
                    <div class="card">
                        <h3 class="font-semibold text-gray-800 mb-4">系统警报</h3>
                        <div class="space-y-3">
                            <div class="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
                                <i class="fas fa-exclamation-triangle text-red-600 mr-3"></i>
                                <div class="flex-1">
                                    <h4 class="font-medium text-red-800 text-sm">库存不足警告</h4>
                                    <p class="text-xs text-red-600">小白菜库存仅剩5斤，建议及时补货</p>
                                </div>
                                <button class="text-red-600 text-sm">处理</button>
                            </div>

                            <div class="flex items-center p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                <i class="fas fa-clock text-yellow-600 mr-3"></i>
                                <div class="flex-1">
                                    <h4 class="font-medium text-yellow-800 text-sm">订单超时提醒</h4>
                                    <p class="text-xs text-yellow-600">订单#SO2024003 分拣时间超过预期30分钟</p>
                                </div>
                                <button class="text-yellow-600 text-sm">查看</button>
                            </div>

                            <div class="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                                <i class="fas fa-check-circle text-green-600 mr-3"></i>
                                <div class="flex-1">
                                    <h4 class="font-medium text-green-800 text-sm">系统运行正常</h4>
                                    <p class="text-xs text-green-600">所有设备运行正常，网络连接稳定</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 会员中心页面 -->
            <div id="members" class="page">
                <div class="header">
                    <div class="flex items-center">
                        <i class="fas fa-arrow-left text-gray-600 mr-3 cursor-pointer" onclick="showPage('profile')"></i>
                        <h1 class="text-xl font-bold text-gray-800">会员中心</h1>
                    </div>
                    <i class="fas fa-plus text-blue-600 cursor-pointer" onclick="showPage('add-member')"></i>
                </div>

                <div class="p-4">
                    <!-- 会员统计 -->
                    <div class="card mb-4">
                        <h3 class="font-semibold text-gray-800 mb-3">账号统计</h3>
                        <div class="grid grid-cols-3 gap-4">
                            <div class="text-center">
                                <div class="text-xl font-bold text-blue-600">12</div>
                                <div class="text-sm text-gray-600">总账号数</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-green-600">8</div>
                                <div class="text-sm text-gray-600">在线账号</div>
                            </div>
                            <div class="text-center">
                                <div class="text-xl font-bold text-orange-600">4</div>
                                <div class="text-sm text-gray-600">离线账号</div>
                            </div>
                        </div>
                    </div>

                    <!-- 子账号列表 -->
                    <div class="space-y-3">
                        <div class="card">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-green-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800">张三</h4>
                                        <p class="text-sm text-gray-600">分拣员 | 在线</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="status-badge status-completed">在线</span>
                                    <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-edit text-gray-600 text-sm"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600">
                                权限：分拣系统、库存查看 | 今日工作：8小时
                            </div>
                        </div>

                        <div class="card">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-blue-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800">李四</h4>
                                        <p class="text-sm text-gray-600">录单员 | 在线</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="status-badge status-completed">在线</span>
                                    <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-edit text-gray-600 text-sm"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600">
                                权限：录单功能、客户管理 | 今日录单：25笔
                            </div>
                        </div>

                        <div class="card">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-gray-600"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-medium text-gray-800">王五</h4>
                                        <p class="text-sm text-gray-600">财务员 | 离线</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="status-badge status-pending">离线</span>
                                    <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-edit text-gray-600 text-sm"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="text-sm text-gray-600">
                                权限：财务管理、数据统计 | 最后登录：2小时前
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 添加子账号页面 -->
            <div id="add-member" class="page">
                <div class="header">
                    <div class="flex items-center">
                        <i class="fas fa-arrow-left text-gray-600 mr-3 cursor-pointer" onclick="showPage('members')"></i>
                        <h1 class="text-xl font-bold text-gray-800">添加子账号</h1>
                    </div>
                    <button class="px-4 py-2 bg-blue-500 text-white rounded-lg text-sm font-medium">保存</button>
                </div>

                <div class="p-4">
                    <!-- 基本信息 -->
                    <div class="card mb-4">
                        <h3 class="font-semibold text-gray-800 mb-4">基本信息</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">姓名</label>
                                <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="请输入姓名">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">手机号</label>
                                <input type="tel" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="请输入手机号">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">角色</label>
                                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option>请选择角色</option>
                                    <option>分拣员</option>
                                    <option>录单员</option>
                                    <option>财务员</option>
                                    <option>管理员</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 权限设置 -->
                    <div class="card">
                        <h3 class="font-semibold text-gray-800 mb-4">权限设置</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-800">入库管理</h4>
                                    <p class="text-sm text-gray-600">管理进货单和入库操作</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-800">录单功能</h4>
                                    <p class="text-sm text-gray-600">AI辅助录单和订单管理</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-800">分拣系统</h4>
                                    <p class="text-sm text-gray-600">商品分拣作业操作</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-800">财务管理</h4>
                                    <p class="text-sm text-gray-600">收益统计和财务分析</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-800">快速售卖</h4>
                                    <p class="text-sm text-gray-600">现货销售POS系统</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>

                            <div class="flex items-center justify-between">
                                <div>
                                    <h4 class="font-medium text-gray-800">中控台</h4>
                                    <p class="text-sm text-gray-600">实时数据监控和分析</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="bottom-nav">
                <div class="nav-item active" onclick="showPage('home')">
                    <i class="fas fa-home text-xl mb-1"></i>
                    <span class="text-xs">功能</span>
                </div>
                
                <div class="nav-item" onclick="toggleAI()">
                    <div class="ai-button">
                        <i class="fas fa-microphone"></i>
                    </div>
                    <span class="text-xs mt-1">AI</span>
                </div>
                
                <div class="nav-item" onclick="showPage('profile')">
                    <i class="fas fa-user text-xl mb-1"></i>
                    <span class="text-xs">我的</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // 显示目标页面
            document.getElementById(pageId).classList.add('active');
            
            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            if (pageId === 'home') {
                document.querySelector('.nav-item:first-child').classList.add('active');
            } else if (pageId === 'profile') {
                document.querySelector('.nav-item:last-child').classList.add('active');
            }
        }

        function toggleAI() {
            alert('AI语音助手功能开发中...');
        }

        function showSortingPage() {
            // 隐藏所有页面
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });

            // 显示分拣页面
            document.getElementById('sorting').classList.add('active');

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
        }

        function addToSortingCart(productName, quantity) {
            alert(`添加 ${productName} ${quantity} 到分拣列表`);
        }
    </script>
</body>
</html>
