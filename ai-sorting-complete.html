<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分拣系统 - 完整原型</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            border: 8px solid #1f2937;
            border-radius: 40px;
            overflow: hidden;
            background: #000;
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #f8fafc;
            position: relative;
            overflow: hidden;
        }
        .status-bar {
            height: 44px;
            background: #1f2937;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
        }
        .page {
            display: none;
            height: calc(100% - 44px - 80px);
            overflow-y: auto;
        }
        .page.active {
            display: block;
        }
        .bottom-nav {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 80px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 10px 0;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .nav-item.active {
            color: #3b82f6;
        }
        .ai-button {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .header {
            background: white;
            padding: 16px 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin: 12px 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }
        .status-processing {
            background: #dbeafe;
            color: #1e40af;
        }
        .landscape-page {
            transform: rotate(90deg);
            transform-origin: center;
            width: 812px;
            height: 375px;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-left: -406px;
            margin-top: -187.5px;
        }
        .landscape-container {
            width: 100%;
            height: 100%;
            display: flex;
            background: #f8fafc;
        }
        .sidebar {
            width: 200px;
            background: white;
            border-right: 1px solid #e5e7eb;
            overflow-y: auto;
        }
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .right-panel {
            width: 280px;
            background: white;
            border-left: 1px solid #e5e7eb;
            display: flex;
            flex-direction: column;
        }
        .product-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
            padding: 16px;
        }
        .product-item {
            aspect-ratio: 1;
            background: white;
            border-radius: 8px;
            padding: 8px;
            border: 1px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .product-item:hover {
            border-color: #3b82f6;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-container">
        <div class="phone-screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <span>9:41</span>
                <span>AI分拣系统</span>
                <div class="flex items-center space-x-1">
                    <i class="fas fa-signal text-sm"></i>
                    <i class="fas fa-wifi text-sm"></i>
                    <i class="fas fa-battery-three-quarters text-sm"></i>
                </div>
            </div>

            <!-- 快速售卖页面（横屏） -->
            <div id="sales" class="page">
                <div class="landscape-page">
                    <div class="landscape-container">
                        <!-- 左侧分类栏 -->
                        <div class="sidebar">
                            <div class="p-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <h2 class="font-bold text-gray-800">快速售卖</h2>
                                    <button onclick="showPage('home')" class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-times text-gray-600"></i>
                                    </button>
                                </div>
                                <p class="text-sm text-gray-600 mt-1">现货销售系统</p>
                            </div>
                            
                            <div class="p-3">
                                <h3 class="font-semibold text-gray-800 mb-3">商品分类</h3>
                                <div class="space-y-2">
                                    <button class="w-full text-left px-3 py-2 bg-green-500 text-white rounded-lg text-sm font-medium">
                                        <i class="fas fa-leaf mr-2"></i>叶菜类
                                    </button>
                                    <button class="w-full text-left px-3 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200">
                                        <i class="fas fa-carrot mr-2"></i>根茎类
                                    </button>
                                    <button class="w-full text-left px-3 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200">
                                        <i class="fas fa-seedling mr-2"></i>菌菇类
                                    </button>
                                    <button class="w-full text-left px-3 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200">
                                        <i class="fas fa-apple-alt mr-2"></i>水果类
                                    </button>
                                </div>
                            </div>

                            <div class="p-3 border-t border-gray-200">
                                <h3 class="font-semibold text-gray-800 mb-3">今日销售</h3>
                                <div class="space-y-2 text-sm">
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">销售额</span>
                                        <span class="font-medium text-green-600">¥8,240</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-gray-600">订单数</span>
                                        <span class="font-medium text-blue-600">45笔</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 中间商品区域 -->
                        <div class="main-content">
                            <div class="p-4 border-b border-gray-200 bg-white">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-gray-800">叶菜类商品</h3>
                                    <div class="flex items-center space-x-4">
                                        <span class="text-sm text-gray-600">库存充足</span>
                                        <button class="px-3 py-1 bg-blue-500 text-white rounded-lg text-sm">
                                            <i class="fas fa-search mr-1"></i>搜索商品
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="flex-1 overflow-y-auto">
                                <div class="product-grid">
                                    <!-- 商品项 -->
                                    <div class="product-item" onclick="addToCart('小白菜', 3.5)">
                                        <img src="https://images.unsplash.com/photo-1540420773420-3366772f4999?w=80&h=60&fit=crop&crop=center" 
                                             class="w-16 h-12 rounded object-cover mb-2" alt="小白菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">小白菜</h4>
                                        <p class="text-xs text-gray-600">库存: 50斤</p>
                                        <div class="mt-1 font-bold text-green-600 text-sm">¥3.5/斤</div>
                                    </div>

                                    <div class="product-item" onclick="addToCart('大白菜', 2.8)">
                                        <img src="https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=80&h=60&fit=crop&crop=center" 
                                             class="w-16 h-12 rounded object-cover mb-2" alt="大白菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">大白菜</h4>
                                        <p class="text-xs text-gray-600">库存: 80斤</p>
                                        <div class="mt-1 font-bold text-green-600 text-sm">¥2.8/斤</div>
                                    </div>

                                    <div class="product-item" onclick="addToCart('菠菜', 4.2)">
                                        <img src="https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=80&h=60&fit=crop&crop=center" 
                                             class="w-16 h-12 rounded object-cover mb-2" alt="菠菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">菠菜</h4>
                                        <p class="text-xs text-gray-600">库存: 30斤</p>
                                        <div class="mt-1 font-bold text-green-600 text-sm">¥4.2/斤</div>
                                    </div>

                                    <div class="product-item" onclick="addToCart('生菜', 3.8)">
                                        <img src="https://images.unsplash.com/photo-1622206151226-18ca2c9ab4a1?w=80&h=60&fit=crop&crop=center" 
                                             class="w-16 h-12 rounded object-cover mb-2" alt="生菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">生菜</h4>
                                        <p class="text-xs text-gray-600">库存: 25斤</p>
                                        <div class="mt-1 font-bold text-green-600 text-sm">¥3.8/斤</div>
                                    </div>

                                    <div class="product-item" onclick="addToCart('韭菜', 5.5)">
                                        <img src="https://images.unsplash.com/photo-1590779033100-9f60a05a013d?w=80&h=60&fit=crop&crop=center" 
                                             class="w-16 h-12 rounded object-cover mb-2" alt="韭菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">韭菜</h4>
                                        <p class="text-xs text-gray-600">库存: 15斤</p>
                                        <div class="mt-1 font-bold text-green-600 text-sm">¥5.5/斤</div>
                                    </div>

                                    <div class="product-item" onclick="addToCart('芹菜', 4.0)">
                                        <img src="https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=80&h=60&fit=crop&crop=center" 
                                             class="w-16 h-12 rounded object-cover mb-2" alt="芹菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">芹菜</h4>
                                        <p class="text-xs text-gray-600">库存: 40斤</p>
                                        <div class="mt-1 font-bold text-green-600 text-sm">¥4.0/斤</div>
                                    </div>

                                    <div class="product-item" onclick="addToCart('油菜', 3.2)">
                                        <img src="https://images.unsplash.com/photo-1590779033100-9f60a05a013d?w=80&h=60&fit=crop&crop=center" 
                                             class="w-16 h-12 rounded object-cover mb-2" alt="油菜">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">油菜</h4>
                                        <p class="text-xs text-gray-600">库存: 35斤</p>
                                        <div class="mt-1 font-bold text-green-600 text-sm">¥3.2/斤</div>
                                    </div>

                                    <div class="product-item" onclick="addToCart('茼蒿', 6.0)">
                                        <img src="https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=80&h=60&fit=crop&crop=center" 
                                             class="w-16 h-12 rounded object-cover mb-2" alt="茼蒿">
                                        <h4 class="font-medium text-gray-800 text-sm text-center">茼蒿</h4>
                                        <p class="text-xs text-gray-600">库存: 20斤</p>
                                        <div class="mt-1 font-bold text-green-600 text-sm">¥6.0/斤</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 右侧购物车 -->
                        <div class="right-panel">
                            <div class="p-4 border-b border-gray-200">
                                <h3 class="font-semibold text-gray-800">购物车</h3>
                                <p class="text-sm text-gray-600">当前订单</p>
                            </div>

                            <div class="flex-1 overflow-y-auto p-4">
                                <div class="space-y-3" id="cart-items">
                                    <div class="text-center text-gray-500 py-8">
                                        <i class="fas fa-shopping-cart text-4xl mb-2"></i>
                                        <p>购物车为空</p>
                                        <p class="text-sm">点击商品添加到购物车</p>
                                    </div>
                                </div>
                            </div>

                            <div class="p-4 border-t border-gray-200">
                                <div class="space-y-3">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">商品总额</span>
                                        <span class="font-medium" id="total-amount">¥0.00</span>
                                    </div>
                                    <button class="w-full py-2 bg-green-500 text-white rounded-lg font-medium text-sm" onclick="checkout()">
                                        <i class="fas fa-cash-register mr-2"></i>立即结算
                                    </button>
                                    <button class="w-full py-2 bg-gray-200 text-gray-700 rounded-lg font-medium text-sm" onclick="clearCart()">
                                        清空购物车
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航栏 -->
            <div class="bottom-nav">
                <div class="nav-item" onclick="showPage('home')">
                    <i class="fas fa-home text-xl mb-1"></i>
                    <span class="text-xs">功能</span>
                </div>
                
                <div class="nav-item" onclick="toggleAI()">
                    <div class="ai-button">
                        <i class="fas fa-microphone"></i>
                    </div>
                    <span class="text-xs mt-1">AI</span>
                </div>
                
                <div class="nav-item" onclick="showPage('profile')">
                    <i class="fas fa-user text-xl mb-1"></i>
                    <span class="text-xs">我的</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let cart = [];
        let totalAmount = 0;

        function showPage(pageId) {
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            if (document.getElementById(pageId)) {
                document.getElementById(pageId).classList.add('active');
            }
            
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            if (pageId === 'home') {
                document.querySelector('.nav-item:first-child').classList.add('active');
            } else if (pageId === 'profile') {
                document.querySelector('.nav-item:last-child').classList.add('active');
            }
        }

        function toggleAI() {
            alert('AI语音助手功能开发中...');
        }

        function addToCart(productName, price) {
            const existingItem = cart.find(item => item.name === productName);
            
            if (existingItem) {
                existingItem.quantity += 1;
                existingItem.total = existingItem.quantity * existingItem.price;
            } else {
                cart.push({
                    name: productName,
                    price: price,
                    quantity: 1,
                    total: price
                });
            }
            
            updateCartDisplay();
        }

        function updateCartDisplay() {
            const cartContainer = document.getElementById('cart-items');
            const totalAmountElement = document.getElementById('total-amount');
            
            if (cart.length === 0) {
                cartContainer.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-shopping-cart text-4xl mb-2"></i>
                        <p>购物车为空</p>
                        <p class="text-sm">点击商品添加到购物车</p>
                    </div>
                `;
                totalAmount = 0;
            } else {
                cartContainer.innerHTML = cart.map(item => `
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <h4 class="font-medium text-gray-800 text-sm">${item.name}</h4>
                            <p class="text-xs text-gray-600">¥${item.price}/斤 × ${item.quantity}斤</p>
                        </div>
                        <div class="text-right">
                            <div class="font-medium text-gray-800 text-sm">¥${item.total.toFixed(2)}</div>
                            <button onclick="removeFromCart('${item.name}')" class="text-red-600 text-xs">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `).join('');
                
                totalAmount = cart.reduce((sum, item) => sum + item.total, 0);
            }
            
            totalAmountElement.textContent = `¥${totalAmount.toFixed(2)}`;
        }

        function removeFromCart(productName) {
            cart = cart.filter(item => item.name !== productName);
            updateCartDisplay();
        }

        function clearCart() {
            cart = [];
            updateCartDisplay();
        }

        function checkout() {
            if (cart.length === 0) {
                alert('购物车为空，请先添加商品');
                return;
            }
            
            alert(`结算成功！总金额：¥${totalAmount.toFixed(2)}`);
            clearCart();
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            showPage('sales');
        });
    </script>
</body>
</html>
