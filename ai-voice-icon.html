<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI语音效果图标</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: radial-gradient(circle at center,
                #1a1a2e 0%,
                #16213e 30%,
                #0f3460 60%,
                #533483 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%,
                rgba(102, 126, 234, 0.1) 0%,
                transparent 50%),
                radial-gradient(circle at 80% 20%,
                rgba(118, 75, 162, 0.1) 0%,
                transparent 50%);
            pointer-events: none;
        }

        .container {
            position: relative;
            width: 300px;
            height: 300px;
        }

        .glass-sphere {
            position: relative;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%,
                rgba(102, 126, 234, 0.15) 0%,
                rgba(118, 75, 162, 0.12) 30%,
                rgba(255, 107, 107, 0.08) 60%,
                rgba(0, 0, 0, 0.3) 100%);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                0 30px 60px rgba(0, 0, 0, 0.2),
                inset 0 0 80px rgba(255, 255, 255, 0.1),
                inset 0 0 40px rgba(102, 126, 234, 0.2),
                0 0 150px rgba(102, 126, 234, 0.4),
                0 0 300px rgba(118, 75, 162, 0.2);
            overflow: hidden;
            animation: sphereFloat 4s ease-in-out infinite;
        }

        .glass-sphere::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 30px;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle,
                rgba(255, 255, 255, 0.6) 0%,
                rgba(255, 255, 255, 0.3) 40%,
                rgba(255, 255, 255, 0.1) 70%,
                transparent 100%);
            border-radius: 50%;
            filter: blur(25px);
        }

        .glass-sphere::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle,
                rgba(255, 255, 255, 0.8) 0%,
                rgba(255, 255, 255, 0.4) 30%,
                rgba(102, 126, 234, 0.3) 60%,
                transparent 100%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            filter: blur(10px);
            animation: centerGlow 2s ease-in-out infinite alternate;
        }

        .wave-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            overflow: hidden;
        }

        .wave {
            position: absolute;
            border-radius: 50%;
            opacity: 0.8;
            animation-timing-function: ease-in-out;
            animation-iteration-count: infinite;
            mix-blend-mode: screen;
        }

        .wave1 {
            width: 80px;
            height: 140px;
            background: radial-gradient(ellipse at center,
                rgba(0, 150, 255, 0.9) 0%,
                rgba(0, 100, 255, 0.7) 40%,
                rgba(0, 150, 255, 0.4) 80%,
                transparent 100%);
            top: 50%;
            left: 50%;
            transform-origin: center center;
            animation: wave1Move 8s infinite linear;
            border-radius: 80px 40px 80px 40px;
            filter: blur(1px);
        }

        .wave2 {
            width: 100px;
            height: 60px;
            background: radial-gradient(ellipse at center,
                rgba(255, 20, 147, 0.9) 0%,
                rgba(255, 0, 100, 0.7) 40%,
                rgba(255, 20, 147, 0.4) 80%,
                transparent 100%);
            top: 50%;
            left: 50%;
            transform-origin: center center;
            animation: wave2Move 10s infinite linear;
            border-radius: 50px 100px 50px 100px;
            filter: blur(1px);
        }

        .wave3 {
            width: 70px;
            height: 110px;
            background: radial-gradient(ellipse at center,
                rgba(0, 255, 150, 0.9) 0%,
                rgba(0, 200, 100, 0.7) 40%,
                rgba(0, 255, 150, 0.4) 80%,
                transparent 100%);
            top: 50%;
            left: 50%;
            transform-origin: center center;
            animation: wave3Move 12s infinite linear;
            border-radius: 35px 70px 105px 35px;
            filter: blur(1px);
        }

        .wave4 {
            width: 90px;
            height: 70px;
            background: radial-gradient(ellipse at center,
                rgba(150, 0, 255, 0.9) 0%,
                rgba(100, 0, 200, 0.7) 40%,
                rgba(150, 0, 255, 0.4) 80%,
                transparent 100%);
            top: 50%;
            left: 50%;
            transform-origin: center center;
            animation: wave4Move 6s infinite linear;
            border-radius: 90px 35px 90px 35px;
            filter: blur(1px);
        }

        .wave5 {
            width: 65px;
            height: 95px;
            background: radial-gradient(ellipse at center,
                rgba(255, 100, 0, 0.9) 0%,
                rgba(255, 150, 0, 0.7) 40%,
                rgba(255, 100, 0, 0.4) 80%,
                transparent 100%);
            top: 50%;
            left: 50%;
            transform-origin: center center;
            animation: wave5Move 14s infinite linear;
            border-radius: 65px 95px 65px 95px;
            filter: blur(1px);
        }

        .energy-core {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 40px;
            height: 40px;
            background: radial-gradient(circle,
                rgba(255, 255, 255, 1) 0%,
                rgba(255, 255, 255, 0.8) 20%,
                rgba(102, 126, 234, 0.6) 40%,
                rgba(118, 75, 162, 0.4) 60%,
                rgba(255, 107, 107, 0.3) 80%,
                transparent 100%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            filter: blur(5px);
            animation: energyPulse 1.5s ease-in-out infinite alternate;
            z-index: 10;
        }

        @keyframes sphereFloat {
            0%, 100% {
                transform: translateY(0px) scale(1);
                box-shadow:
                    0 30px 60px rgba(0, 0, 0, 0.2),
                    inset 0 0 80px rgba(255, 255, 255, 0.1),
                    inset 0 0 40px rgba(102, 126, 234, 0.2),
                    0 0 150px rgba(102, 126, 234, 0.4),
                    0 0 300px rgba(118, 75, 162, 0.2);
            }
            50% {
                transform: translateY(-10px) scale(1.02);
                box-shadow:
                    0 40px 80px rgba(0, 0, 0, 0.25),
                    inset 0 0 100px rgba(255, 255, 255, 0.15),
                    inset 0 0 60px rgba(102, 126, 234, 0.3),
                    0 0 200px rgba(102, 126, 234, 0.5),
                    0 0 400px rgba(118, 75, 162, 0.3);
            }
        }

        @keyframes centerGlow {
            0% {
                opacity: 0.6;
                transform: translate(-50%, -50%) scale(1);
            }
            100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1.2);
            }
        }

        @keyframes energyPulse {
            0% {
                opacity: 0.8;
                transform: translate(-50%, -50%) scale(1);
                filter: blur(5px);
            }
            100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1.5);
                filter: blur(8px);
            }
        }

        @keyframes wave1Move {
            0% {
                transform: translate(0px, 0px) rotate(0deg) scale(0.5);
                opacity: 0.8;
            }
            10% {
                transform: translate(30px, 0px) rotate(36deg) scale(0.8);
                opacity: 0.9;
            }
            20% {
                transform: translate(85px, 0px) rotate(72deg) scale(1);
                opacity: 1;
            }
            30% {
                transform: translate(60px, -60px) rotate(108deg) scale(1.1);
                opacity: 0.9;
            }
            40% {
                transform: translate(0px, -85px) rotate(144deg) scale(1);
                opacity: 0.8;
            }
            50% {
                transform: translate(-60px, -60px) rotate(180deg) scale(0.9);
                opacity: 0.7;
            }
            60% {
                transform: translate(-85px, 0px) rotate(216deg) scale(1.2);
                opacity: 0.8;
            }
            70% {
                transform: translate(-60px, 60px) rotate(252deg) scale(1);
                opacity: 0.9;
            }
            80% {
                transform: translate(0px, 85px) rotate(288deg) scale(1.1);
                opacity: 1;
            }
            90% {
                transform: translate(60px, 60px) rotate(324deg) scale(0.9);
                opacity: 0.8;
            }
            100% {
                transform: translate(85px, 0px) rotate(360deg) scale(1);
                opacity: 0.8;
            }
        }

        @keyframes wave2Move {
            0% {
                transform: translate(0px, 0px) rotate(72deg) scale(0.5);
                opacity: 0.7;
            }
            10% {
                transform: translate(21px, -21px) rotate(108deg) scale(0.8);
                opacity: 0.8;
            }
            20% {
                transform: translate(60px, -60px) rotate(144deg) scale(1);
                opacity: 1;
            }
            30% {
                transform: translate(0px, -85px) rotate(180deg) scale(1.3);
                opacity: 0.9;
            }
            40% {
                transform: translate(-60px, -60px) rotate(216deg) scale(1);
                opacity: 0.8;
            }
            50% {
                transform: translate(-85px, 0px) rotate(252deg) scale(0.8);
                opacity: 0.6;
            }
            60% {
                transform: translate(-60px, 60px) rotate(288deg) scale(1.1);
                opacity: 0.7;
            }
            70% {
                transform: translate(0px, 85px) rotate(324deg) scale(1.2);
                opacity: 0.9;
            }
            80% {
                transform: translate(60px, 60px) rotate(360deg) scale(1);
                opacity: 1;
            }
            90% {
                transform: translate(85px, 0px) rotate(396deg) scale(0.9);
                opacity: 0.8;
            }
            100% {
                transform: translate(60px, -60px) rotate(432deg) scale(1);
                opacity: 0.7;
            }
        }

        @keyframes wave3Move {
            0% {
                transform: translate(0px, 0px) rotate(144deg) scale(0.5);
                opacity: 0.7;
            }
            10% {
                transform: translate(-21px, -21px) rotate(180deg) scale(0.8);
                opacity: 0.8;
            }
            20% {
                transform: translate(-60px, -60px) rotate(216deg) scale(1);
                opacity: 0.9;
            }
            30% {
                transform: translate(-85px, 0px) rotate(252deg) scale(0.9);
                opacity: 1;
            }
            40% {
                transform: translate(-60px, 60px) rotate(288deg) scale(1.2);
                opacity: 0.8;
            }
            50% {
                transform: translate(0px, 85px) rotate(324deg) scale(1);
                opacity: 0.6;
            }
            60% {
                transform: translate(60px, 60px) rotate(360deg) scale(1.1);
                opacity: 0.7;
            }
            70% {
                transform: translate(85px, 0px) rotate(396deg) scale(1);
                opacity: 0.9;
            }
            80% {
                transform: translate(60px, -60px) rotate(432deg) scale(0.9);
                opacity: 1;
            }
            90% {
                transform: translate(0px, -85px) rotate(468deg) scale(1.1);
                opacity: 0.8;
            }
            100% {
                transform: translate(-60px, -60px) rotate(504deg) scale(1);
                opacity: 0.7;
            }
        }

        @keyframes wave4Move {
            0% {
                transform: translate(0px, 0px) rotate(216deg) scale(0.5);
                opacity: 0.6;
            }
            10% {
                transform: translate(-30px, 0px) rotate(252deg) scale(0.8);
                opacity: 0.7;
            }
            20% {
                transform: translate(-85px, 0px) rotate(288deg) scale(1);
                opacity: 0.8;
            }
            30% {
                transform: translate(-60px, 60px) rotate(324deg) scale(1.4);
                opacity: 0.9;
            }
            40% {
                transform: translate(0px, 85px) rotate(360deg) scale(1);
                opacity: 1;
            }
            50% {
                transform: translate(60px, 60px) rotate(396deg) scale(0.7);
                opacity: 0.7;
            }
            60% {
                transform: translate(85px, 0px) rotate(432deg) scale(1.2);
                opacity: 0.8;
            }
            70% {
                transform: translate(60px, -60px) rotate(468deg) scale(1);
                opacity: 0.9;
            }
            80% {
                transform: translate(0px, -85px) rotate(504deg) scale(1.1);
                opacity: 1;
            }
            90% {
                transform: translate(-60px, -60px) rotate(540deg) scale(0.9);
                opacity: 0.8;
            }
            100% {
                transform: translate(-85px, 0px) rotate(576deg) scale(1);
                opacity: 0.6;
            }
        }

        @keyframes wave5Move {
            0% {
                transform: translate(0px, 0px) rotate(288deg) scale(0.5);
                opacity: 0.5;
            }
            10% {
                transform: translate(0px, 30px) rotate(324deg) scale(0.8);
                opacity: 0.6;
            }
            20% {
                transform: translate(0px, 85px) rotate(360deg) scale(1);
                opacity: 0.7;
            }
            30% {
                transform: translate(60px, 60px) rotate(396deg) scale(1.2);
                opacity: 0.9;
            }
            40% {
                transform: translate(85px, 0px) rotate(432deg) scale(1);
                opacity: 1;
            }
            50% {
                transform: translate(60px, -60px) rotate(468deg) scale(0.8);
                opacity: 0.8;
            }
            60% {
                transform: translate(0px, -85px) rotate(504deg) scale(1.3);
                opacity: 0.6;
            }
            70% {
                transform: translate(-60px, -60px) rotate(540deg) scale(1);
                opacity: 0.8;
            }
            80% {
                transform: translate(-85px, 0px) rotate(576deg) scale(1.1);
                opacity: 0.9;
            }
            90% {
                transform: translate(-60px, 60px) rotate(612deg) scale(0.9);
                opacity: 0.7;
            }
            100% {
                transform: translate(0px, 85px) rotate(648deg) scale(1);
                opacity: 0.5;
            }
        }

        .title {
            position: absolute;
            bottom: -60px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                width: 250px;
                height: 250px;
            }
            
            .glass-sphere {
                width: 250px;
                height: 250px;
            }
            
            .title {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="glass-sphere">
            <div class="wave-container">
                <div class="wave wave1"></div>
                <div class="wave wave2"></div>
                <div class="wave wave3"></div>
                <div class="wave wave4"></div>
                <div class="wave wave5"></div>
                <div class="energy-core"></div>
            </div>
        </div>
        <div class="title">AI语音助手</div>
    </div>

    <script>
        // 添加鼠标悬停效果
        const sphere = document.querySelector('.glass-sphere');
        const waves = document.querySelectorAll('.wave');

        sphere.addEventListener('mouseenter', () => {
            sphere.style.transform = 'scale(1.05)';
            sphere.style.transition = 'transform 0.3s ease';
            
            waves.forEach(wave => {
                wave.style.animationDuration = '1.5s';
            });
        });

        sphere.addEventListener('mouseleave', () => {
            sphere.style.transform = 'scale(1)';
            
            waves.forEach((wave, index) => {
                const durations = ['3s', '4s', '3.5s', '2.8s', '3.2s'];
                wave.style.animationDuration = durations[index];
            });
        });

        // 添加点击效果
        sphere.addEventListener('click', () => {
            sphere.style.animation = 'none';
            sphere.offsetHeight; // 触发重排
            sphere.style.animation = 'sphereFloat 4s ease-in-out infinite';
            
            // 临时加速动画
            waves.forEach(wave => {
                wave.style.animationDuration = '1s';
            });
            
            setTimeout(() => {
                waves.forEach((wave, index) => {
                    const durations = ['3s', '4s', '3.5s', '2.8s', '3.2s'];
                    wave.style.animationDuration = durations[index];
                });
            }, 2000);
        });
    </script>
</body>
</html>
