<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI语音效果图标</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: radial-gradient(circle at center,
                #1a1a2e 0%,
                #16213e 30%,
                #0f3460 60%,
                #533483 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%,
                rgba(102, 126, 234, 0.1) 0%,
                transparent 50%),
                radial-gradient(circle at 80% 20%,
                rgba(118, 75, 162, 0.1) 0%,
                transparent 50%);
            pointer-events: none;
        }

        .container {
            position: relative;
            width: 300px;
            height: 300px;
        }

        .glass-sphere {
            position: relative;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%,
                rgba(102, 126, 234, 0.15) 0%,
                rgba(118, 75, 162, 0.12) 30%,
                rgba(255, 107, 107, 0.08) 60%,
                rgba(0, 0, 0, 0.3) 100%);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow:
                0 30px 60px rgba(0, 0, 0, 0.2),
                inset 0 0 80px rgba(255, 255, 255, 0.1),
                inset 0 0 40px rgba(102, 126, 234, 0.2),
                0 0 150px rgba(102, 126, 234, 0.4),
                0 0 300px rgba(118, 75, 162, 0.2);
            overflow: hidden;
            animation: sphereFloat 4s ease-in-out infinite;
        }

        .glass-sphere::before {
            content: '';
            position: absolute;
            top: 15px;
            left: 30px;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle,
                rgba(255, 255, 255, 0.6) 0%,
                rgba(255, 255, 255, 0.3) 40%,
                rgba(255, 255, 255, 0.1) 70%,
                transparent 100%);
            border-radius: 50%;
            filter: blur(25px);
        }

        .glass-sphere::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle,
                rgba(255, 255, 255, 0.8) 0%,
                rgba(255, 255, 255, 0.4) 30%,
                rgba(102, 126, 234, 0.3) 60%,
                transparent 100%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            filter: blur(10px);
            animation: centerGlow 2s ease-in-out infinite alternate;
        }

        .inner-sphere {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 180px;
            height: 180px;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transform-style: preserve-3d;
            perspective: 1000px;
            overflow: hidden;
        }

        .color-layer {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 120px;
            height: 80px;
            transform-origin: center center;
            transform-style: preserve-3d;
            mix-blend-mode: screen;
            opacity: 0.7;
            border-radius: 50%;
        }

        .color1 {
            background: linear-gradient(45deg,
                rgba(0, 150, 255, 0.9) 0%,
                rgba(0, 200, 255, 0.7) 50%,
                rgba(0, 150, 255, 0.9) 100%);
            transform: translate(-50%, -50%) rotateX(0deg) rotateY(0deg);
            animation: sphere3D1 8s infinite linear;
            filter: blur(2px);
        }

        .color2 {
            background: linear-gradient(135deg,
                rgba(255, 20, 147, 0.9) 0%,
                rgba(255, 60, 180, 0.7) 50%,
                rgba(255, 20, 147, 0.9) 100%);
            transform: translate(-50%, -50%) rotateX(60deg) rotateY(0deg);
            animation: sphere3D2 6s infinite linear;
            filter: blur(2px);
        }

        .color3 {
            background: linear-gradient(225deg,
                rgba(0, 255, 150, 0.9) 0%,
                rgba(50, 255, 180, 0.7) 50%,
                rgba(0, 255, 150, 0.9) 100%);
            transform: translate(-50%, -50%) rotateX(120deg) rotateY(0deg);
            animation: sphere3D3 10s infinite linear;
            filter: blur(2px);
        }

        .color4 {
            background: linear-gradient(315deg,
                rgba(150, 0, 255, 0.9) 0%,
                rgba(180, 50, 255, 0.7) 50%,
                rgba(150, 0, 255, 0.9) 100%);
            transform: translate(-50%, -50%) rotateX(180deg) rotateY(0deg);
            animation: sphere3D4 12s infinite linear;
            filter: blur(2px);
        }

        .energy-core {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 30px;
            height: 30px;
            background: radial-gradient(circle,
                rgba(255, 255, 255, 1) 0%,
                rgba(255, 255, 255, 0.8) 30%,
                rgba(255, 255, 255, 0.4) 60%,
                transparent 100%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            filter: blur(3px);
            animation: energyPulse 2s ease-in-out infinite alternate;
            z-index: 10;
        }

        @keyframes sphereFloat {
            0%, 100% {
                transform: translateY(0px) scale(1);
                box-shadow:
                    0 30px 60px rgba(0, 0, 0, 0.2),
                    inset 0 0 80px rgba(255, 255, 255, 0.1),
                    inset 0 0 40px rgba(102, 126, 234, 0.2),
                    0 0 150px rgba(102, 126, 234, 0.4),
                    0 0 300px rgba(118, 75, 162, 0.2);
            }
            50% {
                transform: translateY(-10px) scale(1.02);
                box-shadow:
                    0 40px 80px rgba(0, 0, 0, 0.25),
                    inset 0 0 100px rgba(255, 255, 255, 0.15),
                    inset 0 0 60px rgba(102, 126, 234, 0.3),
                    0 0 200px rgba(102, 126, 234, 0.5),
                    0 0 400px rgba(118, 75, 162, 0.3);
            }
        }

        @keyframes centerGlow {
            0% {
                opacity: 0.6;
                transform: translate(-50%, -50%) scale(1);
            }
            100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1.2);
            }
        }

        @keyframes energyPulse {
            0% {
                opacity: 0.8;
                transform: translate(-50%, -50%) scale(1);
                filter: blur(5px);
            }
            100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1.5);
                filter: blur(8px);
            }
        }

        @keyframes sphere3D1 {
            0% {
                transform: translate(-50%, -50%) rotateX(0deg) rotateY(0deg) rotateZ(0deg);
                opacity: 0.8;
            }
            25% {
                transform: translate(-50%, -50%) rotateX(90deg) rotateY(90deg) rotateZ(90deg);
                opacity: 1;
            }
            50% {
                transform: translate(-50%, -50%) rotateX(180deg) rotateY(180deg) rotateZ(180deg);
                opacity: 0.6;
            }
            75% {
                transform: translate(-50%, -50%) rotateX(270deg) rotateY(270deg) rotateZ(270deg);
                opacity: 0.9;
            }
            100% {
                transform: translate(-50%, -50%) rotateX(360deg) rotateY(360deg) rotateZ(360deg);
                opacity: 0.8;
            }
        }

        @keyframes sphere3D2 {
            0% {
                transform: translate(-50%, -50%) rotateX(60deg) rotateY(0deg) rotateZ(0deg);
                opacity: 0.7;
            }
            33% {
                transform: translate(-50%, -50%) rotateX(180deg) rotateY(120deg) rotateZ(60deg);
                opacity: 0.9;
            }
            66% {
                transform: translate(-50%, -50%) rotateX(300deg) rotateY(240deg) rotateZ(120deg);
                opacity: 0.5;
            }
            100% {
                transform: translate(-50%, -50%) rotateX(420deg) rotateY(360deg) rotateZ(180deg);
                opacity: 0.7;
            }
        }

        @keyframes sphere3D3 {
            0% {
                transform: translate(-50%, -50%) rotateX(120deg) rotateY(0deg) rotateZ(0deg);
                opacity: 0.6;
            }
            20% {
                transform: translate(-50%, -50%) rotateX(192deg) rotateY(72deg) rotateZ(36deg);
                opacity: 0.8;
            }
            40% {
                transform: translate(-50%, -50%) rotateX(264deg) rotateY(144deg) rotateZ(72deg);
                opacity: 1;
            }
            60% {
                transform: translate(-50%, -50%) rotateX(336deg) rotateY(216deg) rotateZ(108deg);
                opacity: 0.7;
            }
            80% {
                transform: translate(-50%, -50%) rotateX(408deg) rotateY(288deg) rotateZ(144deg);
                opacity: 0.9;
            }
            100% {
                transform: translate(-50%, -50%) rotateX(480deg) rotateY(360deg) rotateZ(180deg);
                opacity: 0.6;
            }
        }

        @keyframes sphere3D4 {
            0% {
                transform: translate(-50%, -50%) rotateX(180deg) rotateY(0deg) rotateZ(0deg);
                opacity: 0.8;
            }
            25% {
                transform: translate(-50%, -50%) rotateX(270deg) rotateY(-90deg) rotateZ(-45deg);
                opacity: 0.6;
            }
            50% {
                transform: translate(-50%, -50%) rotateX(360deg) rotateY(-180deg) rotateZ(-90deg);
                opacity: 1;
            }
            75% {
                transform: translate(-50%, -50%) rotateX(450deg) rotateY(-270deg) rotateZ(-135deg);
                opacity: 0.7;
            }
            100% {
                transform: translate(-50%, -50%) rotateX(540deg) rotateY(-360deg) rotateZ(-180deg);
                opacity: 0.8;
            }
        }



        .title {
            position: absolute;
            bottom: -60px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                width: 250px;
                height: 250px;
            }
            
            .glass-sphere {
                width: 250px;
                height: 250px;
            }
            
            .title {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="glass-sphere">
            <div class="inner-sphere">
                <div class="color-layer color1"></div>
                <div class="color-layer color2"></div>
                <div class="color-layer color3"></div>
                <div class="color-layer color4"></div>
                <div class="energy-core"></div>
            </div>
        </div>
        <div class="title">AI语音助手</div>
    </div>

    <script>
        // 添加鼠标悬停效果
        const sphere = document.querySelector('.glass-sphere');
        const waves = document.querySelectorAll('.wave');

        sphere.addEventListener('mouseenter', () => {
            sphere.style.transform = 'scale(1.05)';
            sphere.style.transition = 'transform 0.3s ease';
            
            waves.forEach(wave => {
                wave.style.animationDuration = '1.5s';
            });
        });

        sphere.addEventListener('mouseleave', () => {
            sphere.style.transform = 'scale(1)';
            
            waves.forEach((wave, index) => {
                const durations = ['3s', '4s', '3.5s', '2.8s', '3.2s'];
                wave.style.animationDuration = durations[index];
            });
        });

        // 添加点击效果
        sphere.addEventListener('click', () => {
            sphere.style.animation = 'none';
            sphere.offsetHeight; // 触发重排
            sphere.style.animation = 'sphereFloat 4s ease-in-out infinite';
            
            // 临时加速动画
            waves.forEach(wave => {
                wave.style.animationDuration = '1s';
            });
            
            setTimeout(() => {
                waves.forEach((wave, index) => {
                    const durations = ['3s', '4s', '3.5s', '2.8s', '3.2s'];
                    wave.style.animationDuration = durations[index];
                });
            }, 2000);
        });
    </script>
</body>
</html>
