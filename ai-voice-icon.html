<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI语音效果图标</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: Arial, sans-serif;
        }

        .container {
            position: relative;
            width: 300px;
            height: 300px;
        }

        .glass-sphere {
            position: relative;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.1) 0%,
                rgba(255, 255, 255, 0.05) 50%,
                rgba(255, 255, 255, 0.02) 100%);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.1),
                inset 0 0 50px rgba(255, 255, 255, 0.1),
                0 0 100px rgba(102, 126, 234, 0.3);
            overflow: hidden;
            animation: sphereFloat 4s ease-in-out infinite;
        }

        .glass-sphere::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 40px;
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.4) 0%,
                rgba(255, 255, 255, 0.1) 100%);
            border-radius: 50%;
            filter: blur(20px);
        }

        .wave-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            overflow: hidden;
        }

        .wave {
            position: absolute;
            border-radius: 50%;
            opacity: 0.6;
            animation-timing-function: ease-in-out;
            animation-iteration-count: infinite;
        }

        .wave1 {
            width: 60px;
            height: 120px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation: wave1Move 3s infinite;
            border-radius: 60px 30px 60px 30px;
        }

        .wave2 {
            width: 80px;
            height: 40px;
            background: linear-gradient(45deg, #a8e6cf, #ffd93d);
            top: 30%;
            left: 30%;
            animation: wave2Move 4s infinite;
            border-radius: 40px 80px 40px 80px;
        }

        .wave3 {
            width: 50px;
            height: 90px;
            background: linear-gradient(45deg, #ff8a80, #82b1ff);
            top: 60%;
            left: 70%;
            animation: wave3Move 3.5s infinite;
            border-radius: 25px 50px 75px 25px;
        }

        .wave4 {
            width: 70px;
            height: 50px;
            background: linear-gradient(45deg, #ce93d8, #80cbc4);
            top: 20%;
            left: 60%;
            animation: wave4Move 2.8s infinite;
            border-radius: 70px 25px 70px 25px;
        }

        .wave5 {
            width: 45px;
            height: 75px;
            background: linear-gradient(45deg, #ffab91, #81c784);
            top: 70%;
            left: 20%;
            animation: wave5Move 3.2s infinite;
            border-radius: 45px 75px 45px 75px;
        }

        @keyframes sphereFloat {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-10px) scale(1.02); }
        }

        @keyframes wave1Move {
            0% { 
                transform: translate(-50%, -50%) rotate(0deg) scale(1);
                opacity: 0.6;
            }
            25% { 
                transform: translate(-30%, -70%) rotate(90deg) scale(1.2);
                opacity: 0.8;
            }
            50% { 
                transform: translate(20%, -50%) rotate(180deg) scale(0.8);
                opacity: 0.4;
            }
            75% { 
                transform: translate(-10%, -20%) rotate(270deg) scale(1.1);
                opacity: 0.7;
            }
            100% { 
                transform: translate(-50%, -50%) rotate(360deg) scale(1);
                opacity: 0.6;
            }
        }

        @keyframes wave2Move {
            0% { 
                transform: translate(0%, 0%) rotate(0deg) scale(1);
                opacity: 0.5;
            }
            33% { 
                transform: translate(50%, 100%) rotate(120deg) scale(1.3);
                opacity: 0.8;
            }
            66% { 
                transform: translate(-20%, 50%) rotate(240deg) scale(0.7);
                opacity: 0.3;
            }
            100% { 
                transform: translate(0%, 0%) rotate(360deg) scale(1);
                opacity: 0.5;
            }
        }

        @keyframes wave3Move {
            0% { 
                transform: translate(0%, 0%) rotate(0deg) scale(1);
                opacity: 0.7;
            }
            40% { 
                transform: translate(-80%, -30%) rotate(144deg) scale(1.1);
                opacity: 0.9;
            }
            80% { 
                transform: translate(-30%, -80%) rotate(288deg) scale(0.9);
                opacity: 0.4;
            }
            100% { 
                transform: translate(0%, 0%) rotate(360deg) scale(1);
                opacity: 0.7;
            }
        }

        @keyframes wave4Move {
            0% { 
                transform: translate(0%, 0%) rotate(0deg) scale(1);
                opacity: 0.6;
            }
            30% { 
                transform: translate(-60%, 80%) rotate(108deg) scale(1.4);
                opacity: 0.8;
            }
            60% { 
                transform: translate(40%, 40%) rotate(216deg) scale(0.6);
                opacity: 0.3;
            }
            100% { 
                transform: translate(0%, 0%) rotate(360deg) scale(1);
                opacity: 0.6;
            }
        }

        @keyframes wave5Move {
            0% { 
                transform: translate(0%, 0%) rotate(0deg) scale(1);
                opacity: 0.5;
            }
            35% { 
                transform: translate(70%, -60%) rotate(126deg) scale(1.2);
                opacity: 0.9;
            }
            70% { 
                transform: translate(20%, -20%) rotate(252deg) scale(0.8);
                opacity: 0.4;
            }
            100% { 
                transform: translate(0%, 0%) rotate(360deg) scale(1);
                opacity: 0.5;
            }
        }

        .title {
            position: absolute;
            bottom: -60px;
            left: 50%;
            transform: translateX(-50%);
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                width: 250px;
                height: 250px;
            }
            
            .glass-sphere {
                width: 250px;
                height: 250px;
            }
            
            .title {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="glass-sphere">
            <div class="wave-container">
                <div class="wave wave1"></div>
                <div class="wave wave2"></div>
                <div class="wave wave3"></div>
                <div class="wave wave4"></div>
                <div class="wave wave5"></div>
            </div>
        </div>
        <div class="title">AI语音助手</div>
    </div>

    <script>
        // 添加鼠标悬停效果
        const sphere = document.querySelector('.glass-sphere');
        const waves = document.querySelectorAll('.wave');

        sphere.addEventListener('mouseenter', () => {
            sphere.style.transform = 'scale(1.05)';
            sphere.style.transition = 'transform 0.3s ease';
            
            waves.forEach(wave => {
                wave.style.animationDuration = '1.5s';
            });
        });

        sphere.addEventListener('mouseleave', () => {
            sphere.style.transform = 'scale(1)';
            
            waves.forEach((wave, index) => {
                const durations = ['3s', '4s', '3.5s', '2.8s', '3.2s'];
                wave.style.animationDuration = durations[index];
            });
        });

        // 添加点击效果
        sphere.addEventListener('click', () => {
            sphere.style.animation = 'none';
            sphere.offsetHeight; // 触发重排
            sphere.style.animation = 'sphereFloat 4s ease-in-out infinite';
            
            // 临时加速动画
            waves.forEach(wave => {
                wave.style.animationDuration = '1s';
            });
            
            setTimeout(() => {
                waves.forEach((wave, index) => {
                    const durations = ['3s', '4s', '3.5s', '2.8s', '3.2s'];
                    wave.style.animationDuration = durations[index];
                });
            }, 2000);
        });
    </script>
</body>
</html>
